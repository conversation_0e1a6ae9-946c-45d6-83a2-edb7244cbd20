// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserType {
  CONSUMER
  BUSINESS
}

enum BusinessType {
  RETAIL
  MERCHANT
}

enum Status {
  ACTIVE
  PENDING
  FREEZE
  DEACTIVE
}

enum KycStatus {
  PENDING
  APPROVED
  REJECTED
  DENIED
  CANCELED
  MANUAL_REVIEW
  LOCKED
}

enum KybStatus {
  PENDING
  APPROVED
  REJECTED
  DENIED
  CANCELED
  MANUAL_REVIEW
  LOCKED
}

enum IdentityType {
  KTP
  PASSPORT
  DRIVER_LICENSE
  SIM
}

model User {
  id           String        @id @default(uuid(7)) @db.Uuid
  email        String        @unique
  username     String        @unique
  password     String
  firstName    String        @map("first_name")
  lastName     String        @map("last_name")
  type         UserType
  businessType BusinessType?
  status       Status        @default(PENDING)
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  googleId     String?       @unique
  oauthProvider String?     
  oauthAccessToken String?

  kyc Kyc?

  kyb                Kyb?
  ActivationToken    ActivationToken[]
  PasswordResetToken PasswordResetToken[]

  @@map("user")
}

model Kyc {
  id                String       @id @default(uuid(7)) @db.Uuid
  identityType      IdentityType @map("identity_type")
  identityNumber    String       @map("identity_number")
  firstName         String       @map("first_name")
  lastName          String       @map("last_name")
  birthdate         DateTime
  birthplace        String
  address           String
  state             String
  country           String
  zipNumber         String       @map("zip_number")
  phoneNumber       String       @map("phone_number")
  proofAddressFile  String?      @map("proof_address_file")
  profressOfAddress String?      @map("profress_of_address")
  createdAt         DateTime     @default(now()) @map("created_at")
  updatedAt         DateTime     @updatedAt @map("updated_at")
  userId            String       @unique @map("user_id") @db.Uuid
  kycStatus         KycStatus    @default(PENDING) @map("kyc_status")
  provider          String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc")
}

model Kyb {
  id                 String    @id @default(uuid(7)) @db.Uuid
  namaPerusahaan     String    @map("nama_perusahaan")
  address            String
  ubo                String
  contactInformation String    @map("contact_information")
  legalRegistration  String    @map("legal_registration")
  registrationNumber String    @map("registration_number")
  kybStatus          KybStatus @default(PENDING) @map("kyb_status")
  userId             String    @unique @map("user_id") @db.Uuid
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  provider           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  uboProfiles UboProfile[]

  @@map("kyb")
}

model UboProfile {
  id              String       @id @default(uuid(7)) @db.Uuid
  identityType    IdentityType @map("identity_type")
  identityNumber  String       @map("identity_number")
  firstName       String       @map("first_name")
  lastName        String       @map("last_name")
  address         String
  telephoneNumber String       @map("telephone_number")
  kybId           String       @map("kyb_id") @db.Uuid
  createdAt       DateTime     @default(now()) @map("created_at")
  updatedAt       DateTime     @updatedAt @map("updated_at")

  kyb Kyb @relation(fields: [kybId], references: [id], onDelete: Cascade)

  @@map("ubo_profile")
}

model Role {
  id        String   @id @default(uuid(7)) @db.Uuid
  name      String   @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  admins Admin[]

  @@map("role")
}

model Admin {
  id        String   @id @default(uuid(7)) @db.Uuid
  email     String   @unique
  username  String   @unique
  password  String
  roleId    String   @map("role_id") @db.Uuid
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  role Role @relation(fields: [roleId], references: [id])

  logs Log[]

  @@map("admin")
}

model Log {
  id        String   @id @default(uuid(7)) @db.Uuid
  actions   String
  timestamp DateTime @default(now())
  message   String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  adminId   String?  @map("admin_id") @db.Uuid

  admin Admin? @relation(fields: [adminId], references: [id])

  @@map("log")
}

model ActivationToken {
  id        String   @id @default(uuid(7)) @db.Uuid
  token     String   @unique
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("activation_token")
}

model PasswordResetToken {
  id        String   @id @default(uuid(7)) @db.Uuid
  token     String   @unique
  userId    String   @map("user_id") @db.Uuid
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_token")
}
