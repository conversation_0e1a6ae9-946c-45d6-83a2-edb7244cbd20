/*
  Warnings:

  - Added the required column `first_name` to the `user` table without a default value. This is not possible if the table is not empty.
  - Added the required column `last_name` to the `user` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable: Add columns with default values first
ALTER TABLE "public"."user" ADD COLUMN "first_name" TEXT NOT NULL DEFAULT 'Unknown';
ALTER TABLE "public"."user" ADD COLUMN "last_name" TEXT NOT NULL DEFAULT 'User';

-- Update existing users with placeholder values (you may want to update these manually later)
UPDATE "public"."user" SET
  "first_name" = CASE
    WHEN "username" IS NOT NULL THEN INITCAP(SPLIT_PART("username", '_', 1))
    ELSE 'Unknown'
  END,
  "last_name" = CASE
    WHEN "username" IS NOT NULL AND POSITION('_' IN "username") > 0
    THEN INITCAP(SPLIT_PART("username", '_', 2))
    ELSE 'User'
  END
WHERE "first_name" = 'Unknown' AND "last_name" = 'User';

-- Remove default values to make fields truly required for new records
ALTER TABLE "public"."user" ALTER COLUMN "first_name" DROP DEFAULT;
ALTER TABLE "public"."user" ALTER COLUMN "last_name" DROP DEFAULT;
