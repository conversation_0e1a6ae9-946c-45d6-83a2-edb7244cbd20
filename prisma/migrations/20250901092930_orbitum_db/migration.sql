-- Create<PERSON>num
CREATE TYPE "public"."UserType" AS ENUM ('CONSUMER', 'BUSINESS');

-- CreateEnum
CREATE TYPE "public"."BusinessType" AS ENUM ('RETAIL', 'MERCHANT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "public"."Status" AS ENUM ('ACTIVE', 'PENDING', 'FREEZE', 'DEACTIVE');

-- CreateEnum
CREATE TYPE "public"."KycStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'DENIED', 'CANCELED', 'MANUAL_REVIEW', 'LOCKED');

-- CreateEnum
CREATE TYPE "public"."KybStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'DENIED', 'CANCELED', 'MANUAL_REVIEW', 'LOCKED');

-- CreateEnum
CREATE TYPE "public"."IdentityType" AS ENUM ('KTP', 'PASSPORT', 'DRIVER_LICENSE', 'SIM');

-- CreateTable
CREATE TABLE "public"."user" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "type" "public"."UserType" NOT NULL,
    "businessType" "public"."BusinessType",
    "status" "public"."Status" NOT NULL DEFAULT 'PENDING',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."kyc" (
    "id" UUID NOT NULL,
    "identity_type" "public"."IdentityType" NOT NULL,
    "identity_number" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "birthdate" TIMESTAMP(3) NOT NULL,
    "birthplace" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "zip_number" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,
    "proof_address_file" TEXT,
    "profress_of_address" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "user_id" UUID NOT NULL,
    "kyc_status" "public"."KycStatus" NOT NULL DEFAULT 'PENDING',
    "provider" TEXT,

    CONSTRAINT "kyc_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."kyb" (
    "id" UUID NOT NULL,
    "nama_perusahaan" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "ubo" TEXT NOT NULL,
    "contact_information" TEXT NOT NULL,
    "legal_registration" TEXT NOT NULL,
    "registration_number" TEXT NOT NULL,
    "kyb_status" "public"."KybStatus" NOT NULL DEFAULT 'PENDING',
    "user_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "provider" TEXT,

    CONSTRAINT "kyb_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ubo_profile" (
    "id" UUID NOT NULL,
    "identity_type" "public"."IdentityType" NOT NULL,
    "identity_number" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "telephone_number" TEXT NOT NULL,
    "kyb_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ubo_profile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."role" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."admin" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "role_id" UUID NOT NULL,
    "status" "public"."Status" NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "admin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."log" (
    "id" UUID NOT NULL,
    "actions" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "message" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "admin_id" UUID,

    CONSTRAINT "log_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_email_key" ON "public"."user"("email");

-- CreateIndex
CREATE UNIQUE INDEX "user_username_key" ON "public"."user"("username");

-- CreateIndex
CREATE UNIQUE INDEX "kyc_user_id_key" ON "public"."kyc"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "kyb_user_id_key" ON "public"."kyb"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "role_name_key" ON "public"."role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "admin_email_key" ON "public"."admin"("email");

-- CreateIndex
CREATE UNIQUE INDEX "admin_username_key" ON "public"."admin"("username");

-- AddForeignKey
ALTER TABLE "public"."kyc" ADD CONSTRAINT "kyc_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."kyb" ADD CONSTRAINT "kyb_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ubo_profile" ADD CONSTRAINT "ubo_profile_kyb_id_fkey" FOREIGN KEY ("kyb_id") REFERENCES "public"."kyb"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."admin" ADD CONSTRAINT "admin_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."log" ADD CONSTRAINT "log_admin_id_fkey" FOREIGN KEY ("admin_id") REFERENCES "public"."admin"("id") ON DELETE SET NULL ON UPDATE CASCADE;
