name: Deploy to Fly.io

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

env:
  PRODUCTION_DATABASE_URL: postgresql://neondb_owner:<EMAIL>/orbitum?sslmode=require&channel_binding=require

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
          POSTGRES_USER: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Generate Prisma client
        run: yarn prisma:generate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Run database migrations (test)
        run: yarn prisma db push --force-reset
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Run linting
        run: yarn lint

      - name: Run unit tests
        run: yarn test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Run e2e tests
        run: yarn test:e2e
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Generate Prisma client
        run: yarn prisma:generate
        env:
          DATABASE_URL: ${{ env.PRODUCTION_DATABASE_URL }}

      - name: Build application
        run: yarn build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/
          retention-days: 1

  deploy:
    name: Deploy to Fly.io
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Fly.io CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Set Fly.io secrets
        run: |
          flyctl secrets set DATABASE_URL="${{ env.PRODUCTION_DATABASE_URL }}" --app orbitum-app
          flyctl secrets set NODE_ENV="production" --app orbitum-app
          flyctl secrets set JWT_SECRET="${{ secrets.JWT_SECRET }}" --app orbitum-app
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy to Fly.io
        run: flyctl deploy --remote-only --app orbitum-app
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Verify deployment
        run: |
          echo "🚀 Deployment completed!"
          echo "🌍 App URL: https://orbitum-app.fly.dev"
          echo "📚 API Docs: https://orbitum-app.fly.dev/api/docs"
          echo "❤️ Health Check: https://orbitum-app.fly.dev/api/v1/health"
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  post-deploy:
    name: Post Deployment Tasks
    runs-on: ubuntu-latest
    needs: deploy
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Fly.io CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Run database migrations
        run: flyctl ssh console --app orbitum-app -C "npx prisma migrate deploy"
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Health check
        run: |
          sleep 30
          curl -f https://orbitum-app.fly.dev/api/v1/health || exit 1
          echo "✅ Health check passed!"

      - name: Notify deployment status
        run: |
          echo "🎉 Deployment successful!"
          echo "📊 App Status: $(flyctl status --app orbitum-app)"
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
