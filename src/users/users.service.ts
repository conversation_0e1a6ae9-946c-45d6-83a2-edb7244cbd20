import {
  Injectable,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto, UserType, BusinessType } from './dto/create-user.dto';
import { Status } from './dto/user-response.dto';
import { UserResponseDto } from './dto/user-response.dto';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { EmailService } from 'src/email/email.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { PasswordResetResponseDto } from './dto/password-reset-response.dto';

@Injectable()
export class UsersService {
  private readonly SALT_ROUNDS = 12;
  private readonly TOKEN_EXPIRY_HOURS = 24;
  private readonly RESET_TOKEN_EXPIRY_HOURS = 1;

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
  ) {}

  async register(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    try {
      this.validateBusinessUserRequirements(createUserDto);

      await this.checkUserExists(createUserDto.email, createUserDto.username);
      const hashedPassword = await this.hashPassword(createUserDto.password);

      const user = await this.createUser({
        ...createUserDto,
        password: hashedPassword,
      });

      const activationToken = await this.createActivationToken(user.id);

      await this.emailService.sendActivationEmail(
        user.email,
        user.username,
        activationToken.token,
        user.firstName,
      );

      // Map Prisma enums to DTO enums
      return new UserResponseDto({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        username: user.username,
        type: user.type as UserType,
        businessType: user.businessType as BusinessType,
        status: user.status as Status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        password: user.password,
      });
    } catch (error) {
      if (error.code === 'P2002') {
        throw new ConflictException('User already exists');
      }
      this.handleServiceError(error);
    }
  }

  async activateAccount(token: string): Promise<{ message: string }> {
    // Validate token format
    if (!token || typeof token !== 'string' || token.trim().length === 0) {
      throw new BadRequestException('Invalid activation token format');
    }

    // Decode the token in case it was URL encoded
    const decodedToken = decodeURIComponent(token.trim());

    try {
      const activationToken = await this.prisma.activationToken.findFirst({
        where: {
          token: decodedToken,
          expiresAt: {
            gte: new Date(),
          },
        },
        include: {
          user: true,
        },
      });

      if (!activationToken) {
        throw new BadRequestException('Invalid or expired activation token');
      }

      if (activationToken.user.status === 'ACTIVE') {
        // Clean up the token even if account is already active
        await this.prisma.activationToken
          .delete({
            where: { id: activationToken.id },
          })
          .catch(() => {
            // Ignore deletion errors for already active accounts
          });
        throw new BadRequestException('Account is already activated');
      }

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Update user status
        await prisma.user.update({
          where: { id: activationToken.userId },
          data: { status: 'ACTIVE' },
        });

        // Delete the activation token
        await prisma.activationToken.delete({
          where: { id: activationToken.id },
        });

        // Clean up any other expired tokens for this user
        await prisma.activationToken.deleteMany({
          where: {
            userId: activationToken.userId,
            expiresAt: {
              lt: new Date(),
            },
          },
        });
      });

      return { message: 'Account activated successfully' };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  async resendActivationEmail(email: string): Promise<{ message: string }> {
    // Validate email format
    if (!email || typeof email !== 'string' || !this.isValidEmail(email)) {
      throw new BadRequestException('Invalid email format');
    }

    try {
      const user = await this.findByEmail(email);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (user.status === 'ACTIVE') {
        throw new BadRequestException('Account is already activated');
      }

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Delete all existing activation tokens for this user
        await prisma.activationToken.deleteMany({
          where: { userId: user.id },
        });

        // Create new activation token
        const activationToken = await this.createActivationToken(user.id);

        // Send activation email
        await this.emailService.sendActivationEmail(
          user.email,
          user.username,
          activationToken.token,
          user.firstName,
        );
      });

      return { message: 'Activation email sent successfully' };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  private async createActivationToken(userId: string) {
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + this.TOKEN_EXPIRY_HOURS);

    return this.prisma.activationToken.create({
      data: {
        token,
        expiresAt,
        userId,
      },
    });
  }

  async findByEmail(email: string) {
    return this.prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });
  }

  async findByUsername(username: string) {
    return this.prisma.user.findUnique({ where: { username: username } });
  }

  async findUserByEmailOrUsername(emailOrUsername: string) {
    const isEmail = this.isValidEmail(emailOrUsername);

    if (isEmail) {
      return this.findByEmail(emailOrUsername);
    }

    return this.findByUsername(emailOrUsername);
  }

  async requestPasswordReset(
    forgotPasswordDto: ForgotPasswordDto,
  ): Promise<PasswordResetResponseDto> {
    const { email } = forgotPasswordDto;

    // Validate email format
    if (!this.isValidEmail(email)) {
      throw new BadRequestException('Invalid email format');
    }

    try {
      const user = await this.findByEmail(email);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (user.status !== 'ACTIVE') {
        throw new BadRequestException('Account is not active');
      }

      // Generate secure temporary password
      const temporaryPassword = this.generateSecurePassword();
      const hashedPassword = await this.hashPassword(temporaryPassword);

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Delete any existing password reset tokens
        await prisma.passwordResetToken.deleteMany({
          where: { userId: user.id },
        });

        // Create new reset token
        const resetToken = await this.createPasswordResetToken(user.id);

        // Update user password (but keep it inactive until confirmed)
        await prisma.user.update({
          where: { id: user.id },
          data: { password: hashedPassword },
        });

        // Build confirmation URL
        const confirmationUrl = this.buildPasswordResetConfirmationUrl(
          resetToken.token,
        );

        // Send password reset email
        await this.emailService.sendPasswordResetEmail(
          user.email,
          user.username,
          temporaryPassword,
          confirmationUrl,
          user.firstName,
        );
      });

      return new PasswordResetResponseDto(
        'Password reset email sent successfully',
      );
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  async confirmPasswordReset(token: string): Promise<{ message: string }> {
    // Validate token format
    if (!token || typeof token !== 'string' || token.trim().length === 0) {
      throw new BadRequestException('Invalid reset token format');
    }

    const decodedToken = decodeURIComponent(token.trim());

    try {
      const resetToken = await this.prisma.passwordResetToken.findFirst({
        where: {
          token: decodedToken,
          expiresAt: {
            gte: new Date(),
          },
        },
        include: {
          user: true,
        },
      });

      if (!resetToken) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      // Use transaction to ensure atomicity
      await this.prisma.$transaction(async (prisma) => {
        // Delete the reset token
        await prisma.passwordResetToken.delete({
          where: { id: resetToken.id },
        });

        // Clean up any other expired tokens for this user
        await prisma.passwordResetToken.deleteMany({
          where: {
            userId: resetToken.userId,
            expiresAt: {
              lt: new Date(),
            },
          },
        });
      });

      return {
        message:
          'Password reset confirmed successfully. You can now login with your new password.',
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.handleServiceError(error);
    }
  }

  private validateBusinessUserRequirements(createUserDto: CreateUserDto): void {
    const { type, businessType } = createUserDto;

    // For CONSUMER users, businessType should be undefined or null
    // For BUSINESS users, businessType is optional
    if (
      type === UserType.CONSUMER &&
      businessType !== undefined &&
      businessType !== null
    ) {
      throw new BadRequestException(
        'Business type should not be provided for consumer users',
      );
    }
  }

  private async checkUserExists(
    email: string,
    username: string,
  ): Promise<void> {
    const [existingUserByEmail, existingUserByUsername] = await Promise.all([
      this.findByEmail(email),
      this.findByUsername(username),
    ]);

    if (existingUserByEmail || existingUserByUsername) {
      throw new ConflictException('User already exists');
    }
  }

  private async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.SALT_ROUNDS);
    } catch (error) {
      console.error('Failed to hash password:', error);
      throw new InternalServerErrorException('Failed to hash password');
    }
  }

  private async createUser(userData: CreateUserDto & { password: string }) {
    return this.prisma.user.create({
      data: {
        firstName: userData.firstName.trim(),
        lastName: userData.lastName.trim(),
        email: userData.email.toLowerCase(),
        username: userData.username,
        password: userData.password,
        type: userData.type,
        businessType: userData.businessType,
      },
    });
  }

  private handleServiceError(error: any): never {
    if (
      error instanceof ConflictException ||
      error instanceof BadRequestException
    ) {
      throw error;
    }

    console.error('UserService Error:', error);

    throw new InternalServerErrorException(
      'An error occurred while processing your request',
    );
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private generateSecurePassword(): string {
    const length = 12;
    const charset =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    // Ensure at least one character from each category
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*';

    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }

  private async createPasswordResetToken(userId: string) {
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + this.RESET_TOKEN_EXPIRY_HOURS);

    return this.prisma.passwordResetToken.create({
      data: {
        token,
        expiresAt,
        userId,
      },
    });
  }

  private buildPasswordResetConfirmationUrl(token: string): string {
    const baseUrl =
      process.env.BACKEND_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://orbitum-app.fly.dev'
        : 'http://localhost:3000');

    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    const encodedToken = encodeURIComponent(token);
    return `${cleanBaseUrl}/api/v1/users/reset-password/${encodedToken}`;
  }
}
