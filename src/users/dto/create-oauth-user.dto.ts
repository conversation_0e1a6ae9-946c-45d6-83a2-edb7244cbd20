import {
  IsEmail,
  IsEnum,
  IsO<PERSON>al,
  IsString,
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  Matches,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserType, BusinessType } from './create-user.dto';

export class CreateOAuthUserDto {
  @ApiProperty({
    example: 'John',
    description: 'First name from OAuth provider',
  })
  @IsString({ message: 'First name must be a string' })
  @IsNotEmpty({ message: 'First name is required' })
  @MinLength(1, { message: 'First name must be at least 1 character long' })
  @MaxLength(50, { message: 'First name must not exceed 50 characters' })
  firstName: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name from OAuth provider',
  })
  @IsString({ message: 'Last name must be a string' })
  @IsNotEmpty({ message: 'Last name is required' })
  @MinLength(1, { message: 'Last name must be at least 1 character long' })
  @MaxLength(50, { message: 'Last name must not exceed 50 characters' })
  lastName: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address from OAuth provider',
  })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  email: string;

  @ApiProperty({
    example: 'john.doe',
    description: 'Generated username for the user',
  })
  @IsString()
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Username can only contain letters, numbers, and underscores',
  })
  username: string;

  @ApiProperty({
    example: '**********',
    description: 'Google ID from OAuth provider',
  })
  @IsString({ message: 'Google ID must be a string' })
  @IsNotEmpty({ message: 'Google ID is required' })
  googleId: string;

  @ApiProperty({
    example: 'google',
    description: 'OAuth provider name',
  })
  @IsString({ message: 'OAuth provider must be a string' })
  @IsNotEmpty({ message: 'OAuth provider is required' })
  oauthProvider: string;

  @ApiProperty({
    example: 'ya29.a0AfH6SMC...',
    description: 'OAuth access token',
  })
  @IsString({ message: 'OAuth access token must be a string' })
  @IsNotEmpty({ message: 'OAuth access token is required' })
  oauthAccessToken: string;

  @ApiProperty({
    enum: UserType,
    example: UserType.CONSUMER,
    description: 'Type of the user account',
  })
  @IsEnum(UserType, { message: 'Type must be either CONSUMER or BUSINESS' })
  type: UserType;

  @ApiProperty({
    enum: BusinessType,
    required: false,
    example: BusinessType.RETAIL,
    description: 'Business type (optional for OAuth users)',
  })
  @IsOptional()
  @IsEnum(BusinessType, {
    message: 'Business type must be either RETAIL or MERCHANT',
  })
  businessType?: BusinessType;
}
