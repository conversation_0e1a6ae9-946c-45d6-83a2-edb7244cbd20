import { Injectable, Logger } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor() {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SENDGRID_API_KEY is required');
    }
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  }

  async sendActivationEmail(
    email: string,
    username: string,
    token: string,
    firstName?: string,
  ): Promise<void> {
    const activationUrl = this.buildActivationUrl(token);
    const displayName = firstName || username;

    const msg = {
      to: email,
      from: process.env.SENDGRID_FROM_EMAIL,
      subject: 'Orbitum Account Activation',
      html: this.getActivationEmailTemplate(displayName, activationUrl),
      text: this.getActivationEmailTextVersion(displayName, activationUrl),
    };

    try {
      await sgMail.send(msg);
      this.logger.log(`Activation email sent to ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send activation email to ${email}: ${error}`,
      );
      throw error;
    }
  }

  async sendPasswordResetEmail(
    email: string,
    username: string,
    temporaryPassword: string,
    confirmationUrl: string,
    firstName?: string,
  ): Promise<void> {
    const displayName = firstName || username;

    const msg = {
      to: email,
      from: process.env.SENDGRID_FROM_EMAIL,
      subject: 'Orbitum - Reset Password Anda',
      html: this.getPasswordResetEmailTemplate(
        displayName,
        temporaryPassword,
        confirmationUrl,
      ),
      text: this.getPasswordResetEmailTextVersion(
        displayName,
        temporaryPassword,
        confirmationUrl,
      ),
    };

    try {
      await sgMail.send(msg);
      this.logger.log(`Password reset email sent to ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password reset email to ${email}: ${error}`,
      );
      throw error;
    }
  }

  private getPasswordResetEmailTextVersion(
    username: string,
    temporaryPassword: string,
    confirmationUrl: string,
  ): string {
    return `
  Halo ${username},
  
  Kami telah menerima permintaan reset password untuk akun Orbitum Anda.
  
  Password sementara Anda: ${temporaryPassword}
  
  Untuk mengaktifkan password baru ini, silakan klik link berikut:
  ${confirmationUrl}
  
  PENTING:
  - Password sementara ini hanya berlaku selama 1 jam
  - Setelah login, segera ganti password Anda
  - Jika Anda tidak meminta reset password, abaikan email ini
  
  Tim Orbitum
    `.trim();
  }

  private getPasswordResetEmailTemplate(
    username: string,
    temporaryPassword: string,
    confirmationUrl: string,
  ): string {
    const escapedUsername = this.escapeHtml(username);
    const escapedPassword = this.escapeHtml(temporaryPassword);
    const escapedUrl = this.escapeHtml(confirmationUrl);

    return `
  <!DOCTYPE html>
  <html lang="id">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password Orbitum</title>
  </head>
  <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333333; background-color: #f4f4f4;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f4f4f4;">
      <tr>
        <td align="center" style="padding: 40px 0;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <!-- Header -->
            <tr>
              <td style="padding: 40px 40px 20px 40px; text-align: center;">
                <h1 style="margin: 0; color: #e74c3c; font-size: 28px; font-weight: 600;">
                  Reset Password
                </h1>
                <p style="margin: 10px 0 0 0; color: #666666; font-size: 16px;">
                  Halo, ${escapedUsername}
                </p>
              </td>
            </tr>
            
            <!-- Content -->
            <tr>
              <td style="padding: 0 40px 30px 40px;">
                <p style="margin: 0 0 20px 0; color: #333333; font-size: 16px; line-height: 1.6;">
                  Kami telah menerima permintaan reset password untuk akun Orbitum Anda.
                </p>
                
                <!-- Password Box -->
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #e74c3c; margin: 20px 0;">
                  <p style="margin: 0 0 10px 0; color: #333333; font-weight: bold;">Password Sementara Anda:</p>
                  <div style="background-color: #ffffff; padding: 15px; border-radius: 3px; font-family: 'Courier New', monospace; font-size: 18px; font-weight: bold; color: #e74c3c; text-align: center; border: 2px dashed #e74c3c;">
                    ${escapedPassword}
                  </div>
                </div>
                
                <!-- Button -->
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                  <tr>
                    <td align="center" style="padding: 20px 0;">
                      <a href="${escapedUrl}" 
                         style="display: inline-block; padding: 15px 30px; background-color: #e74c3c; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px; text-align: center; min-width: 200px; box-sizing: border-box;"
                         target="_blank">
                        Aktifkan Password Baru
                      </a>
                    </td>
                  </tr>
                </table>
                
                <!-- Warning -->
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;">
                  <p style="margin: 0; color: #856404; font-size: 14px;">
                    <strong>⚠️ PENTING:</strong><br>
                    • Password sementara ini hanya berlaku selama 1 jam<br>
                    • Setelah login, segera ganti password Anda<br>
                    • Jika Anda tidak meminta reset password, abaikan email ini
                  </p>
                </div>
              </td>
            </tr>
            
            <!-- Footer -->
            <tr>
              <td style="padding: 30px 40px 40px 40px; border-top: 1px solid #e9ecef;">
                <p style="margin: 0; color: #999999; font-size: 12px; text-align: center;">
                  Email ini dikirim secara otomatis, mohon jangan membalas.<br>
                  © 2024 Orbitum. All rights reserved.
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
  </html>
    `.trim();
  }

  private buildActivationUrl(token: string): string {
    // Use backend URL for activation endpoint
    const baseUrl =
      process.env.BACKEND_URL ||
      (process.env.NODE_ENV === 'production'
        ? 'https://orbitum-app.fly.dev'
        : 'http://localhost:3000');

    // Ensure no double slashes and proper URL encoding
    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    const encodedToken = encodeURIComponent(token);
    return `${cleanBaseUrl}/api/v1/users/activate/${encodedToken}`;
  }

  private getActivationEmailTextVersion(
    username: string,
    activationUrl: string,
  ): string {
    return `
Selamat Datang di Orbitum, ${username}!

Terima kasih telah mendaftar di Orbitum. Untuk mengaktifkan akun Anda, silakan klik link berikut atau salin dan tempel ke browser Anda:

${activationUrl}

Catatan: Link aktivasi ini akan kedaluwarsa dalam 24 jam.

Jika Anda tidak mendaftar di Orbitum, abaikan email ini.

Tim Orbitum
    `.trim();
  }

  private getActivationEmailTemplate(
    username: string,
    activationUrl: string,
  ): string {
    // Escape HTML to prevent XSS
    const escapedUsername = this.escapeHtml(username);
    const escapedUrl = this.escapeHtml(activationUrl);

    return `
<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>Aktivasi Akun Orbitum</title>
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333333; background-color: #f4f4f4;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f4f4f4;">
    <tr>
      <td align="center" style="padding: 40px 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <!-- Header -->
          <tr>
            <td style="padding: 40px 40px 20px 40px; text-align: center;">
              <h1 style="margin: 0; color: #2c3e50; font-size: 28px; font-weight: 600;">
                Selamat Datang di Orbitum!
              </h1>
              <p style="margin: 10px 0 0 0; color: #666666; font-size: 16px;">
                Halo, ${escapedUsername}
              </p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 0 40px 30px 40px;">
              <p style="margin: 0 0 20px 0; color: #333333; font-size: 16px; line-height: 1.6;">
                Terima kasih telah mendaftar di Orbitum. Untuk mengaktifkan akun Anda, silakan klik tombol di bawah ini:
              </p>
              
              <!-- Button -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                  <td align="center" style="padding: 20px 0;">
                    <!--[if mso]>
                    <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${escapedUrl}" style="height:50px;v-text-anchor:middle;width:250px;" arcsize="10%" stroke="f" fillcolor="#3498db">
                      <w:anchorlock/>
                      <center style="color:#ffffff;font-family:sans-serif;font-size:16px;font-weight:bold;">Aktivasi Akun</center>
                    </v:roundrect>
                    <![endif]-->
                    <!--[if !mso]><!-->
                    <a href="${escapedUrl}" 
                       style="display: inline-block; padding: 15px 30px; background-color: #3498db; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px; text-align: center; min-width: 200px; box-sizing: border-box;"
                       target="_blank">
                      Aktivasi Akun
                    </a>
                    <!--<![endif]-->
                  </td>
                </tr>
              </table>
              
              <!-- Alternative link -->
              <p style="margin: 30px 0 10px 0; color: #333333; font-size: 14px;">
                Atau salin dan tempel link berikut di browser Anda:
              </p>
              <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #e9ecef; margin: 10px 0 20px 0;">
                <a href="${escapedUrl}" 
                   style="color: #3498db; text-decoration: none; word-break: break-all; font-size: 14px; line-height: 1.4;"
                   target="_blank">
                  ${escapedUrl}
                </a>
              </div>
              
              <p style="margin: 20px 0 0 0; color: #666666; font-size: 14px;">
                <strong>Catatan:</strong> Link aktivasi ini akan kedaluwarsa dalam 24 jam.
              </p>
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="padding: 30px 40px 40px 40px; border-top: 1px solid #e9ecef;">
              <p style="margin: 0; color: #999999; font-size: 12px; text-align: center;">
                Jika Anda tidak mendaftar di Orbitum, abaikan email ini.<br>
                Email ini dikirim secara otomatis, mohon jangan membalas.
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
    `.trim();
  }

  private escapeHtml(text: string): string {
    const map: { [key: string]: string } = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;',
    };
    return text.replace(/[&<>"']/g, (m) => map[m]);
  }
}
