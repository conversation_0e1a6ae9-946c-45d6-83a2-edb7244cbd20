import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class SwaggerAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();

    // Skip auth in development
    if (process.env.NODE_ENV !== 'production') {
      return true;
    }

    // Basic auth for production Swagger
    const auth = request.headers.authorization;
    if (!auth || !auth.startsWith('Basic ')) {
      return false;
    }

    const credentials = Buffer.from(auth.slice(6), 'base64').toString();
    const [username, password] = credentials.split(':');

    // Simple credentials (use environment variables in real app)
    return (
      username === process.env.SWAGGER_USER &&
      password === process.env.SWAGGER_PASS
    );
  }
}
