import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma/prisma.service';

@Injectable()
export class AppService {
  constructor(private prisma: PrismaService) {}

  getHello(): string {
    return 'Hello World!';
  }

  // Example method using Prisma
  async getUsers() {
    // Example Prisma query (uncomment and modify as needed)
    // return await this.prisma.user.findMany();
    return [];
  }
}
