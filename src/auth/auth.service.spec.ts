import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { UnauthorizedException } from '@nestjs/common';
import { UserType } from '../users/dto/create-user.dto';

describe('AuthService - OAuth', () => {
  let service: AuthService;
  let usersService: UsersService;
  let jwtService: JwtService;

  const mockUsersService = {
    findByGoogleId: jest.fn(),
    findByEmail: jest.fn(),
    linkGoogleAccount: jest.fn(),
    createOAuthUser: jest.fn(),
    updateOAuthToken: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get<UsersService>(UsersService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateOAuthLogin', () => {
    const validOAuthData = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      googleId: '*********',
      provider: 'google',
      accessToken: 'valid-access-token',
    };

    it('should create new user when user does not exist', async () => {
      const newUser = {
        id: 'user-id',
        email: '<EMAIL>',
        status: 'ACTIVE',
        ...validOAuthData,
      };

      mockUsersService.findByGoogleId.mockResolvedValue(null);
      mockUsersService.findByEmail.mockResolvedValue(null);
      mockUsersService.createOAuthUser.mockResolvedValue(newUser);

      const result = await service.validateOAuthLogin(validOAuthData);

      expect(mockUsersService.findByGoogleId).toHaveBeenCalledWith('*********');
      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUsersService.createOAuthUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: expect.any(String),
        googleId: '*********',
        oauthProvider: 'google',
        oauthAccessToken: 'valid-access-token',
        type: UserType.CONSUMER,
      });
      expect(result).toEqual(newUser);
    });

    it('should link Google account when user exists by email', async () => {
      const existingUser = {
        id: 'existing-user-id',
        email: '<EMAIL>',
        status: 'ACTIVE',
        oauthProvider: null,
      };

      const linkedUser = {
        ...existingUser,
        googleId: '*********',
        oauthProvider: 'google',
      };

      mockUsersService.findByGoogleId.mockResolvedValue(null);
      mockUsersService.findByEmail.mockResolvedValue(existingUser);
      mockUsersService.linkGoogleAccount.mockResolvedValue(linkedUser);

      const result = await service.validateOAuthLogin(validOAuthData);

      expect(mockUsersService.linkGoogleAccount).toHaveBeenCalledWith(
        'existing-user-id',
        '*********',
        'google',
        'valid-access-token',
      );
      expect(result).toEqual(linkedUser);
    });

    it('should update access token when user exists by Google ID', async () => {
      const existingUser = {
        id: 'existing-user-id',
        email: '<EMAIL>',
        status: 'ACTIVE',
        googleId: '*********',
      };

      mockUsersService.findByGoogleId.mockResolvedValue(existingUser);
      mockUsersService.updateOAuthToken.mockResolvedValue(undefined);

      const result = await service.validateOAuthLogin(validOAuthData);

      expect(mockUsersService.updateOAuthToken).toHaveBeenCalledWith(
        'existing-user-id',
        'valid-access-token',
      );
      expect(result).toEqual(existingUser);
    });

    it('should throw error when user account is not active', async () => {
      const inactiveUser = {
        id: 'user-id',
        email: '<EMAIL>',
        status: 'PENDING',
        googleId: '*********',
      };

      mockUsersService.findByGoogleId.mockResolvedValue(inactiveUser);

      await expect(service.validateOAuthLogin(validOAuthData)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw error when user has different OAuth provider', async () => {
      const existingUser = {
        id: 'existing-user-id',
        email: '<EMAIL>',
        status: 'ACTIVE',
        oauthProvider: 'facebook',
      };

      mockUsersService.findByGoogleId.mockResolvedValue(null);
      mockUsersService.findByEmail.mockResolvedValue(existingUser);

      await expect(service.validateOAuthLogin(validOAuthData)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw error with invalid OAuth data', async () => {
      const invalidData = {
        email: 'invalid-email',
        firstName: '',
        lastName: 'Doe',
        googleId: '*********',
        provider: 'google',
        accessToken: 'valid-access-token',
      };

      await expect(service.validateOAuthLogin(invalidData)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw error with missing required fields', async () => {
      const incompleteData = {
        email: '<EMAIL>',
        firstName: 'John',
        // lastName missing
        googleId: '*********',
        provider: 'google',
        accessToken: 'valid-access-token',
      };

      await expect(service.validateOAuthLogin(incompleteData as any)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });
});
