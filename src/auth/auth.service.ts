import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { UserResponseDto } from '../users/dto/user-response.dto';
import { UserType } from '../users/dto/create-user.dto';

@Injectable()
export class AuthService {
  private readonly JWT_EXPIRY = '24h';
  private readonly TOKEN_EXPIRATION_SECONDS = 24 * 60 * 60;

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async validateUser(emailOrUsername: string, password: string): Promise<any> {
    try {
      const user =
        await this.usersService.findUserByEmailOrUsername(emailOrUsername);

      if (!user) {
        return null;
      }

      if (user.status !== 'ACTIVE') {
        return null;
      }
      const isPasswordValid = await bcrypt.compare(password, user.password);

      return isPasswordValid ? user : null;
    } catch (error) {
      console.error('Error in validateUser:', error);
      return null;
    }
  }

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    const { emailOrUsername, password } = loginDto;

    if (!emailOrUsername || !password) {
      throw new UnauthorizedException(
        'Email/username and password are required',
      );
    }

    const user = await this.validateUser(emailOrUsername, password);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const payload = this.createJwtPayload(user);
    const accessToken = this.generateAccessToken(payload);

    const userResponse = this.createUserResponse(user);

    return new LoginResponseDto(
      accessToken,
      this.TOKEN_EXPIRATION_SECONDS,
      userResponse,
    );
  }

  createJwtPayload(user: any): any {
    return {
      sub: user.id,
      email: user.email,
      username: user.username,
      type: user.type,
      status: user.status,
    };
  }

  generateAccessToken(payload: any): string {
    return this.jwtService.sign(payload, {
      expiresIn: this.JWT_EXPIRY,
    });
  }

  createUserResponse(user: any): UserResponseDto {
    return new UserResponseDto({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      username: user.username,
      type: user.type as any,
      businessType: user.businessType as any,
      status: user.status as any,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      password: user.password,
    });
  }

  async validateOAuthLogin(oauthData: {
    email: string;
    firstName: string;
    lastName: string;
    googleId: string;
    provider: string;
    accessToken: string;
  }): Promise<any> {
    try {
      // Validate input data
      if (!this.isValidOAuthData(oauthData)) {
        throw new UnauthorizedException('Invalid OAuth data provided');
      }

      const { email, firstName, lastName, googleId, provider, accessToken } =
        oauthData;

      // First, try to find user by Google ID
      let user = await this.usersService.findByGoogleId(googleId);

      if (!user) {
        // If not found by Google ID, try by email
        user = await this.usersService.findByEmail(email);

        if (user) {
          // User exists with email but no Google ID - link the accounts
          // Only link if the user doesn't already have a different OAuth provider
          if (user.oauthProvider && user.oauthProvider !== provider) {
            throw new UnauthorizedException(
              `Account is already linked to ${user.oauthProvider}`,
            );
          }

          user = await this.usersService.linkGoogleAccount(
            user.id,
            googleId,
            provider,
            accessToken,
          );
        } else {
          // Create new user
          const username = this.generateUniqueUsername(
            email,
            firstName,
            lastName,
          );
          user = await this.usersService.createOAuthUser({
            email,
            firstName,
            lastName,
            username,
            googleId,
            oauthProvider: provider,
            oauthAccessToken: accessToken,
            type: UserType.CONSUMER,
          });
        }
      } else {
        // User found by Google ID - update access token
        await this.usersService.updateOAuthToken(user.id, accessToken);
      }

      // Ensure user is active
      if (user.status !== 'ACTIVE') {
        throw new UnauthorizedException('Account is not active');
      }

      return user;
    } catch (error) {
      console.error('OAuth validation error:', error);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Failed to process OAuth login');
    }
  }

  private isValidOAuthData(oauthData: any): boolean {
    const { email, firstName, lastName, googleId, provider, accessToken } =
      oauthData;

    return (
      email &&
      typeof email === 'string' &&
      this.isValidEmail(email) &&
      firstName &&
      typeof firstName === 'string' &&
      firstName.trim().length > 0 &&
      lastName &&
      typeof lastName === 'string' &&
      lastName.trim().length > 0 &&
      googleId &&
      typeof googleId === 'string' &&
      googleId.trim().length > 0 &&
      provider &&
      typeof provider === 'string' &&
      provider === 'google' &&
      accessToken &&
      typeof accessToken === 'string' &&
      accessToken.trim().length > 0
    );
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private generateUniqueUsername(
    email: string,
    firstName: string,
    lastName: string,
  ): string {
    const baseUsername = `${firstName.toLowerCase()}.${lastName.toLowerCase()}`;
    const emailPrefix = email.split('@')[0];
    const timestamp = Date.now().toString().slice(-4);

    // Try different username variations
    const candidates = [
      baseUsername,
      `${baseUsername}${timestamp}`,
      emailPrefix,
      `${emailPrefix}${timestamp}`,
    ];

    // Return the first candidate (in a real implementation, you'd check for uniqueness)
    return candidates[0].replace(/[^a-zA-Z0-9_]/g, '_');
  }
}
