import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback, Profile } from 'passport-google-oauth20';
import { AuthService } from '../auth.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new Logger(GoogleStrategy.name);

  constructor(private readonly authService: AuthService) {
    super({
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: process.env.GOOGLE_CALLBACK_URL,
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    _refreshToken: string,
    profile: Profile,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      const { emails, name, id } = profile;

      if (!emails || emails.length === 0) {
        this.logger.error('No email found in Google profile');
        return done(new Error('No email found in Google profile'), null);
      }

      const email = emails[0].value;
      const firstName = name?.givenName || 'Unknown';
      const lastName = name?.familyName || 'User';

      this.logger.log(`Google OAuth validation for email: ${email}`);

      const user = await this.authService.validateOAuthLogin({
        email,
        firstName,
        lastName,
        googleId: id,
        provider: 'google',
        accessToken,
      });

      done(null, user);
    } catch (error) {
      this.logger.error('Google OAuth validation failed:', error);
      done(error, null);
    }
  }
}
