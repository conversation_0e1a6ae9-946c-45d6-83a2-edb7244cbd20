import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback, Profile } from 'passport-google-oauth20';
import { AuthService } from '../auth.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new Logger(GoogleStrategy.name);

  constructor(private readonly authService: AuthService) {
    super({
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: process.env.GOOGLE_CALLBACK_URL,
      scope: ['email', 'profile'],
    });

    // Validate required environment variables
    if (!process.env.GOOGLE_CLIENT_ID) {
      throw new Error('GOOGLE_CLIENT_ID environment variable is required');
    }
    if (!process.env.GOOGLE_CLIENT_SECRET) {
      throw new Error('GOOGLE_CLIENT_SECRET environment variable is required');
    }
    if (!process.env.GOOGLE_CALLBACK_URL) {
      throw new Error('GOOGLE_CALLBACK_URL environment variable is required');
    }
  }

  async validate(
    accessToken: string,
    _refreshToken: string,
    profile: Profile,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      // Validate profile data
      if (!profile) {
        this.logger.error('No profile data received from Google');
        return done(new Error('No profile data received from Google'), null);
      }

      const { emails, name, id } = profile;

      // Validate required fields
      if (!id) {
        this.logger.error('No Google ID found in profile');
        return done(new Error('No Google ID found in profile'), null);
      }

      if (!emails || emails.length === 0) {
        this.logger.error('No email found in Google profile');
        return done(new Error('No email found in Google profile'), null);
      }

      const email = emails[0].value;
      if (!email || !this.isValidEmail(email)) {
        this.logger.error(`Invalid email format: ${email}`);
        return done(new Error('Invalid email format'), null);
      }

      const firstName = name?.givenName?.trim() || 'Unknown';
      const lastName = name?.familyName?.trim() || 'User';

      // Validate access token
      if (!accessToken || typeof accessToken !== 'string') {
        this.logger.error('Invalid access token received');
        return done(new Error('Invalid access token'), null);
      }

      this.logger.log(`Google OAuth validation for email: ${email}`);

      const user = await this.authService.validateOAuthLogin({
        email,
        firstName,
        lastName,
        googleId: id,
        provider: 'google',
        accessToken,
      });

      if (!user) {
        this.logger.error('Failed to validate or create user');
        return done(new Error('Failed to validate or create user'), null);
      }

      done(null, user);
    } catch (error) {
      this.logger.error('Google OAuth validation failed:', error);
      done(error, null);
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
