### Test User Registration with First and Last Name
POST http://localhost:3000/api/v1/users/register
Content-Type: application/json

{
  "firstName": "<PERSON>",
  "lastName": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "username": "johndoe123",
  "password": "SecurePass123!",
  "type": "CONSUMER"
}

### Test User Registration with Business Type
POST http://localhost:3000/api/v1/users/register
Content-Type: application/json

{
  "firstName": "<PERSON>",
  "lastName": "<PERSON>",
  "email": "<EMAIL>",
  "username": "janesmith_biz",
  "password": "BusinessPass456!",
  "type": "BUSINESS",
  "businessType": "RETAIL"
}

### Test Validation Errors (Missing First Name)
POST http://localhost:3000/api/v1/users/register
Content-Type: application/json

{
  "lastName": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "username": "testuser",
  "password": "SecurePass123!",
  "type": "CONSUMER"
}

### Test Validation Errors (Invalid First Name)
POST http://localhost:3000/api/v1/users/register
Content-Type: application/json

{
  "firstName": "John123",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "username": "testuser2",
  "password": "SecurePass123!",
  "type": "CONSUMER"
}
