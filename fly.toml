# fly.toml app configuration file generated for orbitum-app on 2025-09-01T14:02:08+07:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'orbitum-app'
primary_region = 'sin'

[build]

[deploy]
  release_command = "npx prisma migrate deploy"

[env]
  NODE_ENV = 'production'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0

[[vm]]
  memory = '512mb'
  cpu_kind = 'shared'
  cpus = 1

# Health check
[checks]
  [checks.health]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    path = "/api/v1/health"
    port = 3000
    timeout = "5s"
    type = "http"
