# Google OAuth Authentication Setup

This document provides a comprehensive guide for setting up and using Google OAuth authentication in the Orbitum application.

## Overview

The application now supports Google OAuth authentication alongside traditional email/password authentication. Users can:
- Sign up using their Google account
- Link their existing account to Google
- Sign in using Google OAuth

## Prerequisites

1. **Google Cloud Console Setup**
   - Create a project in [Google Cloud Console](https://console.cloud.google.com/)
   - Enable the Google+ API (or Google People API)
   - Create OAuth 2.0 credentials

## Environment Configuration

Add the following environment variables to your `.env` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback
```

### Getting Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth 2.0 Client IDs"
5. Choose "Web application" as the application type
6. Add authorized redirect URIs:
   - Development: `http://localhost:3001/auth/google/callback`
   - Production: `https://yourdomain.com/auth/google/callback`
7. Copy the Client ID and Client Secret to your `.env` file

## API Endpoints

### Initiate Google OAuth
```
GET /auth/google
```
Redirects the user to Google's OAuth consent screen.

### OAuth Callback
```
GET /auth/google/callback
```
Handles the callback from Google and returns a JWT token.

**Response:**
```json
{
  "accessToken": "jwt-token-here",
  "expiresIn": 86400,
  "user": {
    "id": "user-uuid",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "username": "john.doe",
    "type": "CONSUMER",
    "status": "ACTIVE",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## Database Schema

The User model includes the following OAuth-related fields:

```prisma
model User {
  // ... other fields
  googleId         String?  @unique
  oauthProvider    String?
  oauthAccessToken String?
  // ... other fields
}
```

## Authentication Flow

### New User Registration via OAuth
1. User clicks "Sign in with Google"
2. User is redirected to Google OAuth consent screen
3. User grants permissions
4. Google redirects back to `/auth/google/callback`
5. System creates a new user account with:
   - Email from Google profile
   - First/Last name from Google profile
   - Generated unique username
   - Random password (not used for OAuth login)
   - Status set to "ACTIVE" (no email verification needed)
   - Google ID and OAuth provider information

### Existing User Account Linking
1. If a user with the same email already exists:
   - System links the Google account to the existing user
   - Updates the user record with Google ID and OAuth information
   - User can now sign in using either method

### Returning User Authentication
1. User with existing Google account signs in
2. System finds user by Google ID
3. Updates the OAuth access token
4. Returns JWT token for authenticated session

## Security Features

### Input Validation
- Email format validation
- Required field validation
- Google ID uniqueness enforcement
- Access token validation

### Error Handling
- Invalid OAuth data
- Account linking conflicts
- Inactive account detection
- Provider mismatch detection

### Account Protection
- Prevents linking to accounts already associated with different OAuth providers
- Ensures Google accounts can only be linked to one user
- Validates account status before authentication

## Testing

Run the OAuth tests:
```bash
yarn test src/auth/auth.service.spec.ts
```

The test suite covers:
- New user creation via OAuth
- Account linking scenarios
- Error handling for various edge cases
- Security validations

## Frontend Integration

### Redirect Flow (Recommended for Web Apps)
```javascript
// Redirect to Google OAuth
window.location.href = 'http://localhost:3001/auth/google';

// Handle the callback (you may want to redirect to frontend with token)
// The backend can be modified to redirect to frontend with token in URL
```

### API Integration (For SPAs)
```javascript
// Open popup for OAuth
const popup = window.open(
  'http://localhost:3001/auth/google',
  'google-oauth',
  'width=500,height=600'
);

// Listen for callback (requires backend modification to post message)
window.addEventListener('message', (event) => {
  if (event.origin === 'http://localhost:3001') {
    const { token, user } = event.data;
    // Handle successful authentication
    popup.close();
  }
});
```

## Production Considerations

1. **HTTPS Required**: Google OAuth requires HTTPS in production
2. **Domain Verification**: Add your production domain to Google Console
3. **Environment Variables**: Ensure all OAuth environment variables are set
4. **Error Monitoring**: Monitor OAuth failures and user feedback
5. **Rate Limiting**: Consider implementing rate limiting for OAuth endpoints

## Troubleshooting

### Common Issues

1. **"Invalid Client ID"**
   - Verify GOOGLE_CLIENT_ID is correct
   - Check that the client ID matches your Google Console project

2. **"Redirect URI Mismatch"**
   - Ensure GOOGLE_CALLBACK_URL matches the URI in Google Console
   - Check for trailing slashes and protocol (http vs https)

3. **"Access Denied"**
   - User declined OAuth permissions
   - Check OAuth scopes are properly configured

4. **"Account Linking Failed"**
   - User may already have account with different OAuth provider
   - Check error messages for specific conflict details

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will provide detailed OAuth flow logging in the console.
